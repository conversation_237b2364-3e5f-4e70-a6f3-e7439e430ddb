{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Etek会议室预约系统</title>
    <link rel="icon" href="{% static 'favicon.ico' %}" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #1a2a6c;
            --secondary: #b21f1f;
            --accent: #38a169;
            --light: #f8f9fa;
            --dark: #2c3e50;
            --gray: #6c757d;
            --light-gray: #e9ecef;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            color: #333;
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            padding: 20px 0;
            color: white;
            margin-bottom: 20px;
        }
        
        header h1 {
            font-size: clamp(1.8rem, 4vw, 2.5rem);
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        header p {
            font-size: clamp(1rem, 2.5vw, 1.2rem);
            opacity: 0.9;
        }
        
        .switch-container {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            position: relative;
        }
        
        .admin-icon {
            position: absolute;
            top: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-size: 1.2rem;
        }
        
        .admin-icon:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px) scale(1.05);
        }
        
        footer .admin-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        footer .admin-link:hover {
            color: white;
            text-decoration: underline;
        }
        
        .admin-tooltip {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .admin-icon:hover .admin-tooltip {
            opacity: 1;
            visibility: visible;
        }
        
        .switch-btn {
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }
        
        .switch-btn:hover, .switch-btn.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
        }
        
        .switch-btn i {
            margin-right: 6px;
        }
        
        .skip-to-content-link:focus {
            top: 0;
        }
        
        .app-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 900px) {
            .app-container {
                grid-template-columns: 1fr;
            }
        }
        
        .card {
            background: rgba(255, 255, 255, 0.92);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 20px;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
            color: var(--dark);
        }
        
        .card-title i {
            margin-right: 12px;
            font-size: 1.5rem;
            color: var(--primary);
        }
        
        .card-title h2 {
            font-size: clamp(1.2rem, 3vw, 1.5rem);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        /* 表单行布局 */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .form-row .form-group {
            margin-bottom: 0;
        }
        
        /* 时间选择行布局 */
        .time-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }
        
        .time-group {
            display: flex;
            flex-direction: column;
        }
        
        .time-group label {
            font-size: 14px;
            margin-bottom: 5px;
            color: var(--gray);
        }
        
        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
            color: var(--dark);
            font-size: 0.9rem;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(26, 42, 108, 0.2);
        }
        
        .time-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            margin-top: 15px;
        }
        
        .btn:hover {
            background: #142255;
        }
        
        .btn:active {
            transform: scale(0.98);
        }
        
        .btn-secondary {
            background: var(--gray);
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .display-screen {
            background: var(--primary);
            color: white;
            border-radius: 15px;
            padding: 20px;
            min-height: 400px;
            display: flex;
            flex-direction: column;
        }
        

        
        .current-room {
            font-size: clamp(1.5rem, 4vw, 2.2rem);
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .current-time {
            font-size: clamp(2.5rem, 8vw, 4rem);
            text-align: center;
            font-weight: 700;
            margin: 15px 0;
            letter-spacing: 2px;
            text-shadow: 0 4px 6px rgba(0,0,0,0.3);
        }
        
        .current-date {
            font-size: clamp(1rem, 3vw, 1.3rem);
            text-align: center;
            margin-bottom: 25px;
            opacity: 0.9;
        }
        
        .current-event {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .next-event {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
        }
        
        .event-title {
            font-size: clamp(1.1rem, 3vw, 1.4rem);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .event-title i {
            margin-right: 8px;
        }
        
        .event-details {
            font-size: clamp(0.9rem, 2.5vw, 1rem);
            line-height: 1.4;
        }
        
        .reservations-list {
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.92);
            border-radius: 15px;
            padding: 20px;
        }
        
        .reservations-list h3 {
            margin-bottom: 15px;
            color: var(--dark);
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
            display: flex;
            align-items: center;
            font-size: clamp(1.1rem, 3vw, 1.3rem);
        }
        
        .reservations-list h3 i {
            margin-right: 8px;
            color: var(--primary);
        }
        
        .reservation-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            align-items: center;
            gap: 10px;
        }
        
        @media (max-width: 600px) {
            .reservation-item {
                grid-template-columns: 1fr;
                gap: 5px;
                text-align: center;
            }
        }
        
        .reservation-item:last-child {
            border-bottom: none;
        }
        
        .reservation-item:hover {
            background: #f9f9f9;
        }
        
        .reservation-time {
            font-weight: bold;
            color: var(--primary);
            font-size: 0.9rem;
        }
        
        .reservation-title {
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .reservation-booker {
            font-style: italic;
            color: #666;
            font-size: 0.8rem;
        }
        
        .confirmation {
            background: var(--accent);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            text-align: center;
            display: none;
        }
        
        .error-message {
            background: #fee2e2;
            color: #b91c1c;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            display: none;
            font-size: 0.9rem;
        }
        
        .management-section {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            padding: 25px;
            margin-bottom: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .reservation-management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .reservation-management-title {
            display: flex;
            align-items: center;
            color: var(--dark);
        }
        
        .reservation-management-title i {
            margin-right: 12px;
            font-size: 1.8rem;
            color: var(--primary);
        }
        
        .reservation-management-title h2 {
            font-size: 1.8rem;
            margin: 0;
        }
        
        .reservation-filters {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-select, .search-input {
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 0.9rem;
            background: white;
            transition: border-color 0.3s;
        }
        
        .filter-select:focus, .search-input:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(26, 42, 108, 0.1);
        }
        
        .search-input {
            min-width: 200px;
        }
        
        .reservation-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .reservation-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .reservation-item {
            display: grid;
            grid-template-columns: 1.2fr 1fr 1.3fr 2fr 1fr 1.2fr 1.5fr;
            gap: 12px;
            padding: 12px 16px;
            align-items: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
            font-size: 0.9rem;
            min-height: 50px;
        }
        
        .reservation-item:hover {
            background-color: #f8f9fa;
        }
        
        .reservation-item-header {
            background: var(--primary);
            color: white;
            font-weight: 600;
            border-bottom: none;
            font-size: 0.95rem;
            padding: 14px 16px;
        }
        
        .reservation-item-header:hover {
            background: var(--primary);
        }
        
        .reservation-item:last-child {
            border-bottom: none;
        }
        
        .reservation-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
        }
        
        .status-upcoming {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .status-current {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .status-past {
            background: #f5f5f5;
            color: #757575;
        }
        
        .reservation-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-edit {
            background: #2196f3;
            color: white;
        }
        
        .btn-edit:hover {
            background: #1976d2;
        }
        
        .btn-delete {
            background: #f44336;
            color: white;
        }
        
        .btn-delete:hover {
            background: #d32f2f;
        }
        
        .no-reservations {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray);
        }
        
        .no-reservations i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .no-reservations p {
            font-size: 1.1rem;
        }
        
        .export-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
        
        .export-btn:hover {
            background: linear-gradient(135deg, #218838, #1ea080);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }
        
        .export-btn i {
            font-size: 1rem;
        }
        
        @media (max-width: 768px) {
             .reservation-management-header {
                 flex-direction: column;
                 align-items: stretch;
             }
             
             .reservation-filters {
                 justify-content: center;
                 flex-direction: column;
                 gap: 10px;
             }
             
             .filter-select, .search-input {
                 width: 100%;
                 min-width: unset;
             }
             
             .reservation-item {
                 grid-template-columns: 1fr;
                 gap: 8px;
                 text-align: left;
                 padding: 15px;
                 font-size: 0.85rem;
             }
             
             .reservation-item > div {
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 padding: 5px 0;
                 border-bottom: 1px solid #f0f0f0;
             }
             
             .reservation-item > div:last-child {
                 border-bottom: none;
                 justify-content: center;
                 padding-top: 10px;
             }
             
             .reservation-item > div::before {
                 content: attr(data-label) ': ';
                 font-weight: 600;
                 color: var(--primary);
                 min-width: 80px;
             }
             
             .reservation-actions::before {
                 display: none;
             }
             
             .reservation-item-header {
                 display: none;
             }
             
             .reservation-stats {
                 grid-template-columns: 1fr;
             }
             
             .stat-card {
                 padding: 15px;
             }
             
             .stat-number {
                  font-size: 1.5rem;
              }
          }
        
        .management-title {
            display: flex;
            align-items: center;
            color: var(--dark);
        }
        
        .management-title i {
            margin-right: 10px;
            font-size: 1.5rem;
            color: var(--primary);
        }
        
        .management-title h2 {
            font-size: clamp(1.2rem, 3vw, 1.5rem);
            margin: 0;
        }
        
        .item-list {
            max-height: 500px;
            overflow-y: auto;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8e9ea;
        }
        
        .item {
            display: grid;
            grid-template-columns: 1.5fr 1fr 1.2fr 1.5fr 1fr 1fr 120px;
            padding: 15px 12px;
            border-bottom: 1px solid #e8e9ea;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
            background: white;
        }
        
        @media (max-width: 768px) {
            .item {
                grid-template-columns: 1fr;
                gap: 5px;
                text-align: center;
                padding: 15px 10px;
            }
            
            .item-header {
                display: none; /* 在移动端隐藏表头 */
            }
            
            .item:not(.item-header) div::before {
                content: attr(data-label);
                display: block;
                font-weight: bold;
                margin-bottom: 5px;
                color: var(--primary);
                font-size: 0.9rem;
            }
            
            .item-actions {
                margin-top: 10px;
            }
        }
        
        .item-header {
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 15px 12px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .item:hover {
            background: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .item-actions {
            display: flex;
            gap: 6px;
            justify-content: center;
        }
        
        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
            min-width: 50px;
            justify-content: center;
        }
        
        .btn-edit {
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            color: white;
            box-shadow: 0 2px 6px rgba(79, 195, 247, 0.3);
        }
        
        .btn-edit:hover {
            background: linear-gradient(135deg, #29b6f6 0%, #0288d1 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 195, 247, 0.4);
        }
        
        .btn-delete {
            background: linear-gradient(135deg, #ef5350 0%, #e53935 100%);
            color: white;
            box-shadow: 0 2px 6px rgba(239, 83, 80, 0.3);
        }
        
        .btn-delete:hover {
            background: linear-gradient(135deg, #e53935 0%, #c62828 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 83, 80, 0.4);
        }
        
        .form-container {
            background: rgba(240, 244, 248, 0.8);
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid var(--accent);
            margin-top: 20px;
            display: none;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .no-items {
            text-align: center;
            padding: 40px 20px;
            color: var(--gray);
            font-size: 1rem;
        }
        
        .no-items i {
            font-size: 2.5rem;
            margin-bottom: 10px;
            opacity: 0.5;
        }
        
        footer {
            text-align: center;
            color: white;
            margin-top: 30px;
            padding: 15px;
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        /* 移动端优化 */
        @media (max-width: 480px) {
            body {
                padding: 5px;
            }
            
            .card {
                padding: 15px;
            }
            
            .switch-container {
                gap: 5px;
            }
            
            .switch-btn {
                padding: 8px 12px;
                font-size: 0.8rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .time-container {
                grid-template-columns: 1fr;
            }
        }
        
        /* 移动端布局优化 - 确保预约表单在视图中 */
        @media (max-width: 900px) {
            .app-container {
                display: flex;
                flex-direction: column;
            }
            
            #bookingFormCard {
                order: 2; /* 将预约表单放在第二位置 */
                margin-top: 20px;
            }
            
            .display-screen {
                order: 1; /* 将会议室使用情况放在第一位置 */
            }
        }
        
        /* 今日会议安排样式 */
        #reservationsContainer {
            margin-top: 10px;
        }
        
        .today-reservation-row {
            display: grid;
            grid-template-columns: 140px 2fr 120px 100px 120px;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            background: transparent;
            font-size: 1.05rem;
        }
        
        .today-reservation-row:last-child {
            border-bottom: none;
        }
        
        .today-reservation-time {
            display: flex;
            align-items: center;
            font-weight: bold;
            color: #222;
            gap: 8px;
            justify-content: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 6px;
        }
        
        .status-ongoing {
            background: #38a169; /* 绿色 - 进行中 */
        }
        
        .status-upcoming {
            background: #e53e3e; /* 红色 - 即将开始 */
        }
        
        .status-ended {
            background: #bdbdbd; /* 灰色 - 已结束 */
        }
        
        .today-reservation-title {
            color: #222;
            text-align: center;
        }
        
        .today-reservation-room,
        .today-reservation-booker,
        .today-reservation-department {
            text-align: center;
            padding-left: 8px;
        }
        
        @media (max-width: 768px) {
            /* 隐藏表头行 */
            .today-reservation-row[style*="font-weight: bold"] {
                display: none;
            }
            
            .today-reservation-row {
                display: block;
                padding: 15px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                margin-bottom: 10px;
                background: #f9f9f9;
            }
            
            .today-reservation-row:last-child {
                border-bottom: 1px solid #e0e0e0;
            }
            
            .today-reservation-time,
            .today-reservation-title,
            .today-reservation-room,
            .today-reservation-booker,
            .today-reservation-department {
                display: block;
                text-align: left;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            }
            
            .today-reservation-time::before {
                content: "时间: ";
                font-weight: bold;
                color: #666;
            }
            
            .today-reservation-title::before {
                content: "主题: ";
                font-weight: bold;
                color: #666;
            }
            
            .today-reservation-room::before {
                content: "会议室: ";
                font-weight: bold;
                color: #666;
            }
            
            .today-reservation-booker::before {
                content: "预约人: ";
                font-weight: bold;
                color: #666;
            }
            
            .today-reservation-department::before {
                content: "预约部门: ";
                font-weight: bold;
                color: #666;
            }
            
            .today-reservation-department {
                border-bottom: none;
            }
        }
        
        /* 密码对话框样式 */
        .password-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .password-modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        
        .password-modal h3 {
            margin-bottom: 20px;
            color: var(--dark);
        }
        
        .password-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .password-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .password-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .password-btn-confirm {
            background: var(--primary);
            color: white;
        }
        
        .password-btn-cancel {
            background: var(--gray);
            color: white;
        }
        
        /* 会议室情况日历对话框样式 */
        .calendar-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .calendar-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .calendar-nav {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .calendar-nav button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .calendar-nav button:hover {
            background: #142255;
        }
        
        .calendar-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--dark);
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .calendar-day-header {
            background: var(--primary);
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .calendar-day {
            background: white;
            min-height: 80px;
            padding: 5px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .calendar-day:hover {
            background: #f0f8ff;
        }
        
        .calendar-day.other-month {
            background: #f5f5f5;
            color: #999;
        }
        
        .calendar-day.today {
            background: #e3f2fd;
            border: 2px solid var(--primary);
        }
        
        .calendar-day-number {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .calendar-event {
            background: var(--primary);
            color: white;
            font-size: 0.7rem;
            padding: 2px 4px;
            border-radius: 3px;
            margin-bottom: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 24px;
            cursor: pointer;
            color: var(--gray);
            transition: color 0.3s;
        }
        
        .close-modal:hover {
            color: var(--dark);
        }
        
        /* 可用时间段样式 */
        .available-time-slots {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            min-height: 40px;
            align-items: center;
        }
        
        .time-slot {
            padding: 6px 12px;
            background: #e9ecef;
            border-radius: 6px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .time-slot.available {
            background: #d4edda;
            color: #155724;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .time-slot.available:hover {
            background: #c3e6cb;
            transform: translateY(-1px);
        }
        
        .time-slot.occupied {
            background: #f8d7da;
            color: #721c24;
        }
        
        .time-slot.past-time {
            background: #e9ecef;
            color: #6c757d;
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 7天预约状态日历样式 */
        .week-calendar {
            margin-top: 30px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .week-calendar h3 {
            margin: 0 0 20px 0;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 状态显示屏中的7天日历样式 */
        .week-calendar-status {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .week-calendar-status h4 {
            margin: 0 0 15px 0;
            color: rgb(246,195,67);
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            background: rgb(49,64,123);
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .week-calendar-status h4:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* 会议室状态卡片样式 */
        .room-status-section {
            margin-top: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .room-status-title {
            margin: 0 0 20px 0;
            color: rgb(246,195,67);
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            background: rgb(49,64,123);
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .room-status-title:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .room-status-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .room-stat {
            text-align: center;
            color: white;
        }
        
        .room-stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .room-stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .room-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 10px;
        }
        
        .room-status-card {
            background: rgb(76,89,140);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            border-left: 4px solid #4CAF50;
            position: relative;
            overflow: hidden;
        }
        
        .room-status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        }
        
        .room-status-card.occupied {
            border-left-color: #f44336;
        }
        
        .room-status-card.maintenance {
            border-left-color: #ff9800;
        }
        
        .room-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(255,255,255,0.2);
        }
        
        .room-card-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
        }
        
        .room-card-status {
            padding: 4px 10px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.8rem;
            text-transform: uppercase;
        }
        
        .room-card-status.available {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }
        
        .room-card-status.occupied {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }
        
        .room-card-status.maintenance {
            background: rgba(255, 152, 0, 0.2);
            color: #ff9800;
            border: 1px solid #ff9800;
        }
        
        .room-card-status.loading {
            background: rgba(158, 158, 158, 0.2);
            color: #9e9e9e;
            border: 1px solid rgba(158, 158, 158, 0.3);
            font-size: 0.75rem;
        }
        
        .room-card-status.loading i {
            margin-right: 4px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .room-card-info {
            margin-bottom: 10px;
        }
        
        .room-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            color: rgba(255,255,255,0.9);
            font-size: 0.85rem;
        }
        
        .room-info-item i {
            margin-right: 6px;
            width: 14px;
            color: rgb(246,195,67);
        }
        
        @media (max-width: 768px) {
            .room-cards-grid {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 8px;
            }
            
            .room-status-stats {
                gap: 15px;
            }
            
            .room-stat-number {
                font-size: 1.8rem;
            }
            
            .room-status-card {
                padding: 12px;
            }
            
            .room-card-name {
                font-size: 1.1rem;
            }
        }
        
        .week-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 12px;
        }
        
        .week-day {
            text-align: center;
            padding: 15px 8px;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.3s;
        }
        
        .week-day.today {
            background: var(--primary);
            color: white;
        }
        
        .week-day.has-meetings {
            background: #fff3cd;
            border: 2px solid #ffc107;
        }
        
        .week-day.today.has-meetings {
            background: var(--primary);
            border: 2px solid #ffffff;
        }
        
        /* 状态显示屏中的week-day样式 */
        .week-calendar-status .week-day {
            padding: 10px 6px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 12px;
        }
        
        .week-calendar-status .week-day.today {
            background: rgba(255, 255, 255, 0.4);
            color: white;
            font-weight: bold;
        }
        
        .week-calendar-status .week-day.has-meetings {
            background: rgba(255, 193, 7, 0.3);
            border: 1px solid rgba(255, 193, 7, 0.6);
        }
        
        .week-calendar-status .week-day.today.has-meetings {
            background: rgba(255, 255, 255, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }
        
        .week-day-name {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 5px;
            opacity: 0.8;
        }
        
        .week-day-date {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .week-day-count {
            font-size: 11px;
            padding: 2px 6px;
            background: rgba(0,0,0,0.1);
            border-radius: 10px;
            display: inline-block;
        }
        
        .week-day.today .week-day-count {
            background: rgba(255,255,255,0.3);
        }
        
        @media (max-width: 768px) {
            .week-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 8px;
            }
            
            .week-day {
                padding: 10px 5px;
            }
            
            .week-day-date {
                font-size: 16px;
            }
        }
        
        @media (max-width: 480px) {
            .week-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        /* 时间选择器样式 */
        .time-picker-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        .time-picker {
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 15px;
            background: #fafafa;
        }
        
        .time-picker-title {
            text-align: center;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--dark);
            font-size: 16px;
        }
        
        .time-selector {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: white;
        }
        
        .time-option {
            padding: 12px 8px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid #e0e0e0;
            position: relative;
            font-size: 14px;
            font-weight: 500;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .time-option:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }
        
        .time-option.selected {
            background: #1e3a8a;
            color: white;
            border-color: #1e3a8a;
            font-weight: 600;
        }
        
        .time-option.booked {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
            cursor: not-allowed;
            font-weight: 600;
        }
        
        .time-option.booked::after {
            content: "已预约";
            position: absolute;
            top: 2px;
            right: 2px;
            background: rgba(0,0,0,0.4);
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 0 6px 0 6px;
            color: white;
            font-weight: normal;
        }
        
        .time-option.past-time {
            background: #e9ecef;
            color: #6c757d;
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #e9ecef;
        }
        
        .time-option.past-time::after {
            content: "已过期";
            position: absolute;
            top: 0;
            right: 0;
            background: rgba(0,0,0,0.3);
            font-size: 0.7rem;
            padding: 2px 5px;
            border-radius: 0 6px 0 6px;
        }
        
        .time-option.disabled {
            background: #f8f9fa;
            color: #6c757d;
            opacity: 0.6;
            cursor: not-allowed;
            border-color: #dee2e6;
        }
        
        .time-option.disabled::after {
            content: "请选择会议室";
            position: absolute;
            top: 0;
            right: 0;
            background: rgba(0,0,0,0.3);
            font-size: 0.6rem;
            padding: 2px 4px;
            border-radius: 0 6px 0 6px;
            color: white;
        }
        
        .time-input-group {
            margin-top: 15px;
        }
        
        .time-error {
            color: #e53e3e;
            font-size: 0.9rem;
            margin-top: 5px;
            display: none;
        }
        
        #selectedTimeDisplay {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            text-align: center;
            font-weight: 600;
            color: var(--dark);
        }
        

        
        /* 表单响应式设计 */
        @media (max-width: 768px) {
            .form-row,
            .time-row,
            .time-picker-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .time-selector {
                grid-template-columns: repeat(3, 1fr);
                gap: 6px;
            }
            
            .time-option {
                padding: 10px 6px;
                font-size: 13px;
                min-height: 35px;
            }
        }
        
        @media (max-width: 480px) {
            .time-selector {
                grid-template-columns: repeat(2, 1fr);
                gap: 5px;
            }
            
            .time-option {
                padding: 8px 4px;
                font-size: 12px;
                min-height: 30px;
            }
            
            .form-row .form-group {
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <a href="#main-content" class="skip-to-content-link" style="position: absolute; left: 0; top: -40px; padding: 8px; background: #38a169; color: white; z-index: 100; transition: top 0.3s;">跳转至主要内容</a>
    <div class="container">
        <header>
            <h1><i class="fas fa-calendar-alt"></i> Etek会议室预约系统</h1>
        </header>
        
        <div class="switch-container">
            <button class="switch-btn active" onclick="showBookingSection()">
                <i class="fas fa-plus-circle"></i> 预约会议
            </button>
            <button class="switch-btn" onclick="showSection('management')">
                <i class="fas fa-list-alt"></i> 预约管理
            </button>
            <button class="switch-btn" onclick="showRoomStatus()">
                <i class="fas fa-calendar-check"></i> 会议室日历
            </button>
            <button class="switch-btn" onclick="window.open('/status_display/', '_blank')">
                <i class="fas fa-tv"></i> 状态显示屏
            </button>
        </div>
        
        <!-- 预约界面 -->
        <div id="bookingSection">
            <div id="main-content" tabindex="-1">
            <div class="app-container">
                <div class="display-screen">
                    <div class="current-room">会议室使用情况</div>
                    <div class="current-time" id="currentTime">10:30:45</div>
                    <div class="current-date" id="currentDate">2025年7月13日 星期四</div>
                    
                    <div class="current-event" id="currentEvent">
                        <div class="event-title">
                            <i class="fas fa-microphone-alt"></i> 
                            <span id="currentEventTitle">暂无进行中的会议</span>
                        </div>
                        <div class="event-details" id="currentEventDetails">
                            <div>会议室空闲中</div>
                        </div>
                    </div>
                    
                    <div class="next-event" id="nextEvent">
                        <div class="event-title">
                            <i class="fas fa-clock"></i> 
                            <span>下一个会议</span>
                        </div>
                        <div class="event-details" id="nextEventDetails">
                            <div>暂无后续会议</div>
                        </div>
                    </div>
                    
                    <!-- 7天预约状态日历 -->
                    <div class="week-calendar-status">
                        <h4><i class="fas fa-calendar-week"></i> 未来7天预约状态</h4>
                        <div class="week-grid" id="weekCalendar">
                            <!-- 7天日历将动态生成 -->
                        </div>
                    </div>
                    
                    <!-- 会议室状态卡片 -->
                    <div class="room-status-section">
                        <h4 class="room-status-title"><i class="fas fa-door-open"></i> 会议室状态</h4>
                        
                        <div class="room-status-stats">
                            <div class="room-stat">
                                <div class="room-stat-number" id="totalRoomsCount">0</div>
                                <div class="room-stat-label">会议室总数</div>
                            </div>
                            <div class="room-stat">
                                <div class="room-stat-number" id="availableRoomsCount" style="color: #4CAF50;">0</div>
                                <div class="room-stat-label">可用会议室</div>
                            </div>
                        </div>
                        
                        <div class="room-cards-grid" id="roomStatusCards">
                            <!-- 会议室状态卡片将动态生成 -->
                        </div>
                    </div>
                </div>
                
                <div class="card" id="bookingFormCard">
                    <div class="card-title">
                        <i class="fas fa-calendar-plus"></i>
                        <h2>新建预约</h2>
                    </div>
                    
                    <form id="bookingForm">
                        {% csrf_token %}
                        <div class="form-row">
                            <div class="form-group">
                                <label for="meetingRoom">选择会议室</label>
                                <select id="meetingRoom" required>
                                    <option value="">-- 请选择会议室 --</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="bookingDate">选择日期</label>
                                <input type="date" id="bookingDate" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>选择时间段</label>
                            <div class="time-picker-container">
                                <div class="time-picker">
                                    <div class="time-picker-title">开始时间</div>
                                    <div class="time-selector" id="startTimeSelector">
                                        <!-- 开始时间选项将在这里生成 -->
                                    </div>
                                </div>
                                
                                <div class="time-picker">
                                    <div class="time-picker-title">结束时间</div>
                                    <div class="time-selector" id="endTimeSelector">
                                        <!-- 结束时间选项将在这里生成 -->
                                    </div>
                                </div>
                            </div>
                            <div class="time-input-group">
                                <div class="time-error" id="timeError">
                                    <i class="fas fa-exclamation-circle"></i> 结束时间必须晚于开始时间
                                </div>
                                <div id="selectedTimeDisplay">
                                    已选择: <span id="startTimeDisplay">--:--</span> 至 <span id="endTimeDisplay">--:--</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="meetingTitle">会议主题</label>
                            <input type="text" id="meetingTitle" placeholder="请输入会议主题" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="bookerName">预约人</label>
                                <input type="text" id="bookerName" placeholder="请输入预约人姓名" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="bookerDepartment">预约部门</label>
                                <input type="text" id="bookerDepartment" placeholder="请输入预约部门" required>
                            </div>
                        </div>
                        
 

                        <button type="submit" class="btn">提交预约</button>
                        
                        <div class="confirmation" id="confirmation">
                            <i class="fas fa-check-circle"></i> 预约成功！您的会议已安排。
                        </div>
                        
                        <div class="error-message" id="errorMessage"></div>
                    </form>
                </div>
            </div>
            
            <div class="reservations-list">
                <h3><i class="fas fa-list"></i> 今日会议安排</h3>
                <div id="reservationsContainer">
                    <!-- 预约列表将动态生成 -->
                </div>
            </div>
            </div>
        </div>
        
        <!-- 预约管理界面 -->
        <div id="managementSection" class="management-section">
            <div class="reservation-management-header">
                <div class="reservation-management-title">
                    <i class="fas fa-list-alt"></i>
                    <h2>预约管理</h2>
                </div>
                <div class="reservation-filters">
                    <select id="filterRoom" class="filter-select">
                        <option value="">所有会议室</option>
                    </select>
                    <select id="filterDate" class="filter-select">
                        <option value="">所有日期</option>
                        <option value="today">今天</option>
                        <option value="tomorrow">明天</option>
                        <option value="week">本周</option>
                    </select>
                    <input type="text" id="searchBooker" class="search-input" placeholder="搜索预约人...">
                </div>
                <button id="addReservationBtn" class="btn">添加预约</button>
                <button id="exportReservationsBtn" class="btn btn-secondary" style="height:32px;align-self:center;margin-left:10px;padding:4px 16px;font-size:0.95rem;line-height:1.2;min-width:unset;width:auto;">
                    <i class="fas fa-file-export"></i> 导出表格
                </button>
            </div>
            
            <div class="reservation-stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalReservations">0</div>
                    <div class="stat-label">总预约数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="todayReservations">0</div>
                    <div class="stat-label">今日预约</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="upcomingReservations">0</div>
                    <div class="stat-label">即将到来</div>
                </div>
            </div>
            
            <div class="reservation-list">
                <div class="reservation-item reservation-item-header">
                    <div>会议室</div>
                    <div>日期</div>
                    <div>时间</div>
                    <div>会议主题</div>
                    <div>预约人</div>
                    <div>预约部门</div>
                    <div>操作</div>
                </div>
                <div id="managementContainer">
                    <!-- 管理列表将动态生成 -->
                </div>
            </div>
            
            <div class="no-reservations" id="noReservations" style="display: none;">
                <i class="fas fa-calendar-times"></i>
                <p>暂无预约记录</p>
            </div>
            
            <div id="reservationFormContainer" class="form-container">
                <h3 id="reservationFormTitle">添加预约</h3>
                <form id="reservationForm">
                    <input type="hidden" id="reservationId">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="editMeetingRoom">会议室</label>
                            <select id="editMeetingRoom" required>
                                <option value="">-- 请选择会议室 --</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="editBookingDate">日期</label>
                            <input type="date" id="editBookingDate" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="editStartTime">开始时间</label>
                            <input type="time" id="editStartTime" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="editEndTime">结束时间</label>
                            <input type="time" id="editEndTime" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="editMeetingTitle">会议主题</label>
                            <input type="text" id="editMeetingTitle" placeholder="请输入会议主题" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="editBookerName">预约人</label>
                            <input type="text" id="editBookerName" placeholder="请输入预约人姓名" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="editBookerDepartment">预约部门</label>
                            <input type="text" id="editBookerDepartment" placeholder="请输入预约部门">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">保存预约</button>
                        <button type="button" id="cancelReservationForm" class="btn btn-secondary">取消</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 会议室管理界面 -->
        <div id="roomsSection" class="management-section">
            <div class="management-header">
                <div class="management-title">
                    <i class="fas fa-door-open"></i>
                    <h2>会议室管理</h2>
                </div>
                <button id="addRoomBtn" class="btn">添加会议室</button>
            </div>
            
            <div class="item-list">
                <div class="item item-header">
                    <div>名称</div>
                    <div>容量</div>
                    <div>设备</div>
                    <div>状态</div>
                    <div>操作</div>
                </div>
                <div id="roomsContainer">
                    <!-- 会议室列表将动态生成 -->
                </div>
            </div>
            
            <div id="roomFormContainer" class="form-container">
                <h3 id="roomFormTitle">添加会议室</h3>
                <form id="roomForm">
                    <input type="hidden" id="roomId">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="roomName">会议室名称</label>
                            <input type="text" id="roomName" placeholder="例如：创新厅" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="roomCapacity">最大容量</label>
                            <input type="number" id="roomCapacity" min="1" placeholder="例如：10" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="roomDescription">描述</label>
                            <input type="text" id="roomDescription" placeholder="会议室简要描述">
                        </div>
                        
                        <div class="form-group">
                            <label for="roomEquipment">设备</label>
                            <input type="text" id="roomEquipment" placeholder="例如：投影仪, 白板">
                        </div>
                        
                        <div class="form-group">
                            <label for="roomStatus">状态</label>
                            <select id="roomStatus">
                                <option value="available">可用</option>
                                <option value="maintenance">维护中</option>
                                <option value="unavailable">不可用</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">保存会议室</button>
                        <button type="button" id="cancelRoomForm" class="btn btn-secondary">取消</button>
                    </div>
                </form>
            </div>
        </div>
        


        <!-- 会议室情况日历对话框 -->
        <div id="calendarModal" class="calendar-modal">
            <div class="calendar-modal-content">
                <span class="close-modal" onclick="closeCalendarModal()">&times;</span>
                <div class="calendar-header">
                    <div class="calendar-nav">
                        <button onclick="changeMonth(-1)">&lt;</button>
                        <span class="calendar-title" id="calendarTitle">2025年1月</span>
                        <button onclick="changeMonth(1)">&gt;</button>
                    </div>
                </div>
                <div class="calendar-grid" id="calendarGrid">
                    <!-- 日历内容将动态生成 -->
                </div>
            </div>
        </div>
        
        <!-- 密码验证对话框 -->
        <div id="passwordModal" class="password-modal">
            <div class="password-modal-content">
                <h3>请输入管理员密码</h3>
                <input type="password" id="passwordInput" class="password-input" placeholder="请输入密码">
                <div class="password-buttons">
                    <button class="password-btn password-btn-confirm" onclick="verifyPassword()">确认</button>
                    <button class="password-btn password-btn-cancel" onclick="closePasswordModal()">取消</button>
                </div>
            </div>
        </div>
        
        <footer>
            <p>Etek会议室预约系统 &copy; 2025 | 技术支持: Haixi.Zhao | <a href="/admin/" class="admin-link">管理后台</a></p>
        </footer>
    </div>

    <script>
        // 全局变量
        let rooms = [];
        let reservations = [];
        let currentEditingRoom = null;
        let pendingSection = null;
        let currentCalendarDate = new Date();
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            setInterval(updateDateTime, 1000);
        });
        
        // 页面加载时恢复会议室选择并初始化时间选项
        window.addEventListener('DOMContentLoaded', function() {
            const lastRoom = localStorage.getItem('selectedRoom');
            const meetingRoomSelect = document.getElementById('meetingRoom');
            if (lastRoom && meetingRoomSelect) {
                meetingRoomSelect.value = lastRoom;
            }
            // 自动选中第一个可用会议室
            if (meetingRoomSelect && !meetingRoomSelect.value) {
                for (let i = 0; i < meetingRoomSelect.options.length; i++) {
                    if (meetingRoomSelect.options[i].value) {
                        meetingRoomSelect.value = meetingRoomSelect.options[i].value;
                        break;
                    }
                }
            }
            // 触发一次相关更新（如果有会议室选中）
            generateTimeOptions();
            updateAvailableTimeSlots();
        });
        
        // 初始化页面
        async function initializePage() {
            updateDateTime();
            setBookingDateRange();
            
            // 先显示占位符，提供即时的视觉反馈
            generatePlaceholderCards();
            setupTimeSelectors();
            
            // 异步加载数据
            try {
                await loadRooms();
                await loadReservations();
                
                // 如果没有会议室数据，创建默认数据
                if (!rooms || rooms.length === 0) {
                    console.log('没有会议室数据，创建默认数据');
                    await createDefaultRooms();
                    await loadRooms();
                }
                
                // 数据加载完成后更新界面
                updateRoomStatusCards();
                generateRoomOptions();
                populateFilterOptions();
                displayReservations();
                displayRooms();
                updateCurrentEvent();
                generateWeekCalendar();
                updateRoomAvailability();
                updateAvailableTimeSlots();
                
                // 确保在所有DOM元素准备好后更新统计数据
                setTimeout(() => {
                    updateReservationStats();
                }, 100);
                
            } catch (error) {
                console.error('初始化页面时出错:', error);
                // 即使出错也保持占位符显示
            }
            
            showSection(localStorage.getItem('lastSection') || 'booking');
            
            const savedRoom = localStorage.getItem('selectedRoom');
            if (savedRoom) {
                const roomSelect = document.getElementById('meetingRoom');
                roomSelect.value = savedRoom;
                // Manually trigger change to update time slots after setting room from storage
                roomSelect.dispatchEvent(new Event('change'));
            }
        }
        
        // 设置预约日期范围（7天内）
        function setBookingDateRange() {
            const today = new Date();
            const dateInput = document.getElementById('bookingDate');
            dateInput.valueAsDate = today;
            dateInput.min = today.toISOString().split('T')[0];
            const maxDate = new Date();
            maxDate.setDate(today.getDate() + 7);
            dateInput.max = maxDate.toISOString().split('T')[0];
        }
        
        // 生成时间选项（整点和半点）
        function generateTimeOptions() {
            const startSelector = document.getElementById('startTimeSelector');
            const endSelector = document.getElementById('endTimeSelector');
            
            if (!startSelector || !endSelector) {
                console.error('时间选择器元素未找到');
                return;
            }
            
            // 清空现有选项
            startSelector.innerHTML = '';
            endSelector.innerHTML = '';
            
            const times = [
                '08:00', '08:30', '09:00', '09:30', 
                '10:00', '10:30', '11:00', '11:30',
                '12:00', '12:30', '13:00', '13:30',
                '14:00', '14:30', '15:00', '15:30',
                '16:00', '16:30', '17:00', '17:30',
                '18:00'
            ];
            
            // 获取当前预约数据，如果没有选择会议室则为空数组
            const currentReservations = getReservationsForCurrentRoomAndDate() || [];
            
            // 判断是否为今天，过滤掉已过时间
            const bookingDate = document.getElementById('bookingDate').value;
            const todayStr = new Date().toISOString().split('T')[0];
            let nowMinutes = 0;
            if (bookingDate === todayStr) {
                const now = new Date();
                nowMinutes = now.getHours() * 60 + now.getMinutes();
            }
            
            // 生成开始时间选项
            times.forEach(time => {
                // 如果为今天且时间已过，则不显示
                if (bookingDate === todayStr) {
                    const [h, m] = time.split(':').map(Number);
                    const tMinutes = h * 60 + m;
                    if (tMinutes <= nowMinutes) return;
                }
                
                const option = document.createElement('div');
                option.className = 'time-option';
                option.textContent = time;
                option.dataset.time = time;
                
                // 检查该时间点是否已被预约
                const isBooked = currentReservations.some(res => {
                    return time >= res.start && time < res.end;
                });
                
                if (isBooked) {
                    option.classList.add('booked');
                    option.title = '该时间段已被预订';
                } else {
                    option.addEventListener('click', function() {
                        document.querySelectorAll('#startTimeSelector .time-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });
                        this.classList.add('selected');
                        document.getElementById('startTimeDisplay').textContent = this.dataset.time;
                        updateEndTimeOptions(this.dataset.time);
                        validateTimeSelection();
                    });
                }
                
                startSelector.appendChild(option);
            });
            
            // 清空结束时间选项
            document.getElementById('endTimeDisplay').textContent = '--:--';
        }
        
        // 设置时间选择器事件
        function setupTimeSelectors() {
            const roomSelect = document.getElementById('meetingRoom');
            const dateInput = document.getElementById('bookingDate');
            
            // 监听会议室、日期变化
        [roomSelect, dateInput].forEach(element => {
            if (element) {
                element.addEventListener('change', function() {
                    generateTimeOptions();
                    updateAvailableTimeSlots();
                });
            }
        });
        }
        
        // 当日期或会议室改变时重新生成时间选项
        document.getElementById('bookingDate').addEventListener('change', function() {
            generateTimeOptions();
            updateAvailableTimeSlots();
        });
        
        // 记住会议室选择
        document.getElementById('meetingRoom').addEventListener('change', function() {
            localStorage.setItem('selectedRoom', this.value);
            generateTimeOptions();
            updateAvailableTimeSlots();
        });
        
        // 选择开始时间
        function selectStartTime(time) {
            // 清除之前的选择
            document.querySelectorAll('#startTimeSelector .time-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // 选中当前时间
            const selectedOption = document.querySelector(`#startTimeSelector .time-option[data-time="${time}"]`);
            if (selectedOption) {
                selectedOption.classList.add('selected');
            }
            
            // 更新显示
            document.getElementById('startTimeDisplay').textContent = time;
            
            // 更新结束时间选项
            updateEndTimeOptions(time);
            
            // 检查时间冲突
            validateTimeSelection();
            updateAvailableTimeSlots();
        }
        
        // 更新结束时间选项
        function updateEndTimeOptions(startTime) {
            const endSelector = document.getElementById('endTimeSelector');
            endSelector.innerHTML = '';
            
            const times = [
                '08:00', '08:30', '09:00', '09:30', 
                '10:00', '10:30', '11:00', '11:30',
                '12:00', '12:30', '13:00', '13:30',
                '14:00', '14:30', '15:00', '15:30',
                '16:00', '16:30', '17:00', '17:30',
                '18:00'
            ];
            
            const currentReservations = getReservationsForCurrentRoomAndDate();
            const bookingDate = document.getElementById('bookingDate').value;
            const todayStr = new Date().toISOString().split('T')[0];
            let nowMinutes = 0;
            if (bookingDate === todayStr) {
                const now = new Date();
                nowMinutes = now.getHours() * 60 + now.getMinutes();
            }
            
            const [startH, startM] = startTime.split(':').map(Number);
            const startMinutes = startH * 60 + startM;
            
            times.forEach(time => {
                const [h, m] = time.split(':').map(Number);
                const tMinutes = h * 60 + m;
                
                // 结束时间必须晚于开始时间
                if (tMinutes <= startMinutes) return;
                
                // 如果为今天且时间已过，则不显示
                if (bookingDate === todayStr && tMinutes <= nowMinutes) return;
                
                const option = document.createElement('div');
                option.className = 'time-option';
                option.textContent = time;
                option.dataset.time = time;
                
                // 检查该时间段是否与现有预约冲突
                const hasConflict = currentReservations.some(res => {
                    const resStart = res.start;
                    const resEnd = res.end;
                    return (startTime < resEnd && time > resStart);
                });
                
                if (hasConflict) {
                    option.classList.add('booked');
                    option.title = '该时间段与现有预约冲突';
                } else {
                    option.addEventListener('click', function() {
                        document.querySelectorAll('#endTimeSelector .time-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });
                        this.classList.add('selected');
                        document.getElementById('endTimeDisplay').textContent = this.dataset.time;
                        validateTimeSelection();
                    });
                }
                
                endSelector.appendChild(option);
            });
            
            // 清空结束时间显示
            document.getElementById('endTimeDisplay').textContent = '--:--';
        }
        
        // 选择结束时间
        function selectEndTime(time) {
            // 清除之前的选择
            document.querySelectorAll('#endTimeSelector .time-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // 选中当前时间
            const selectedOption = document.querySelector(`#endTimeSelector .time-option[data-time="${time}"]`);
            if (selectedOption) {
                selectedOption.classList.add('selected');
            }
            
            // 更新显示
            document.getElementById('endTimeDisplay').textContent = time;
            
            // 检查时间冲突
            validateTimeSelection();
        }
        
        // 验证时间选择
        function validateTimeSelection() {
            const startTime = getSelectedStartTime();
            const endTime = getSelectedEndTime();
            const timeError = document.getElementById('timeError');
            
            if (startTime && endTime) {
                if (startTime >= endTime) {
                    timeError.style.display = 'block';
                } else {
                    timeError.style.display = 'none';
                }
            } else {
                timeError.style.display = 'none';
            }
        }
        
        // 获取选中的开始时间
        function getSelectedStartTime() {
            const selectedOption = document.querySelector('#startTimeSelector .time-option.selected');
            return selectedOption ? selectedOption.dataset.time : null;
        }
        
        // 获取选中的结束时间
        function getSelectedEndTime() {
            const selectedOption = document.querySelector('#endTimeSelector .time-option.selected');
            return selectedOption ? selectedOption.dataset.time : null;
        }
        
        // 获取当前选择的会议室和日期的预约
        function getReservationsForCurrentRoomAndDate() {
            const room = document.getElementById('meetingRoom').value;
            const date = document.getElementById('bookingDate').value;
            
            if (!room || !date) return [];
            
            return reservations.filter(res => 
                res.room === room && 
                res.date === date
            );
        }
        
        // 检查时间段是否冲突
        function checkTimeConflict(start, end, currentReservations) {
            for (const res of currentReservations) {
                // 时间冲突的条件：新会议开始时间 < 已有会议结束时间 且 新会议结束时间 > 已有会议开始时间
                if (start < res.end && end > res.start) {
                    return res;
                }
            }
            return null;
        }
        
        // 更新可用时间段显示
        function updateAvailableTimeSlots() {
            const roomId = document.getElementById('meetingRoom').value;
            const date = document.getElementById('bookingDate').value;
            const container = document.getElementById('availableTimeSlots');
            
            if (!roomId || !date) {
                container.innerHTML = '<div class="time-slot">请先选择会议室和日期</div>';
                return;
            }
            
            // 获取该会议室当天的预约
            const dayReservations = reservations.filter(res => 
                res.room === roomId && res.date === date
            );
            
            // 获取当前时间信息
            const todayStr = new Date().toISOString().split('T')[0];
            const now = new Date();
            const nowMinutes = now.getHours() * 60 + now.getMinutes();
            
            // 生成时间段（8:00-18:00，每30分钟一个时间段）
            const timeSlots = [];
            for (let hour = 8; hour < 18; hour++) {
                for (let minute of [0, 30]) {
                    const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                    const endHour = minute === 30 ? hour + 1 : hour;
                    const endMinute = minute === 30 ? 0 : 30;
                    const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;
                    const timeMinutes = hour * 60 + minute;
                    
                    // 如果是今天且时间已过，则跳过不显示
                    if (date === todayStr && timeMinutes <= nowMinutes) {
                        continue;
                    }
                    
                    // 检查是否被占用
                    const isOccupied = dayReservations.some(res => 
                        startTime < res.end && endTime > res.start
                    );
                    
                    timeSlots.push({
                        start: startTime,
                        end: endTime,
                        available: !isOccupied
                    });
                }
            }
            
            // 渲染时间段
            container.innerHTML = timeSlots.map(slot => {
                let className = 'time-slot';
                let title = '';
                let onclick = '';
                
                if (slot.available) {
                    className += ' available';
                    title = '点击选择此时间段';
                    onclick = `selectTimeSlot('${slot.start}', '${slot.end}')`;
                } else {
                    className += ' occupied';
                    title = '此时间段已被预约';
                }
                
                return `<div class="${className}" title="${title}" ${onclick ? `onclick="${onclick}"` : ''}>
                    ${slot.start}-${slot.end}
                </div>`;
            }).join('');
        }
        
        // 选择时间段
        function selectTimeSlot(startTime, endTime) {
            selectStartTime(startTime);
            selectEndTime(endTime);
        }
        

        
        // 生成会议室状态卡片
        // 生成会议室状态卡片占位符
        function generatePlaceholderCards() {
            const container = document.getElementById('roomStatusCards');
            const totalCountEl = document.getElementById('totalRoomsCount');
            const availableCountEl = document.getElementById('availableRoomsCount');
            
            if (!container || !totalCountEl || !availableCountEl) {
                console.log('会议室状态卡片容器元素未找到');
                return;
            }
            
            // 显示占位卡片
            const placeholderCards = [
                { name: '会议室A', capacity: 10, equipment: '投影仪、白板、电话会议系统' },
                { name: '会议室B', capacity: 20, equipment: '大屏显示器、音响系统、视频会议设备' },
                { name: '会议室C', capacity: 50, equipment: '舞台、音响系统、多媒体设备、同声传译' },
                { name: '培训室', capacity: 30, equipment: '投影仪、音响、可移动桌椅' }
            ];
            
            // 设置初始统计数据
            totalCountEl.textContent = placeholderCards.length;
            availableCountEl.textContent = placeholderCards.length;
            
            container.innerHTML = '';
            
            // 生成占位卡片
            placeholderCards.forEach((room, index) => {
                const card = document.createElement('div');
                card.className = 'room-status-card available';
                card.setAttribute('data-room-index', index);
                
                card.innerHTML = `
                    <div class="room-card-header">
                        <div class="room-card-name">${room.name}</div>
                        <div class="room-card-status loading">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                    <div class="room-card-info">
                        <div class="room-info-item">
                            <i class="fas fa-users"></i>
                            <span>容量: ${room.capacity}人</span>
                        </div>
                        <div class="room-info-item">
                            <i class="fas fa-tv"></i>
                            <span>设备: ${room.equipment}</span>
                        </div>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }
        
        // 更新会议室状态卡片数据
        function updateRoomStatusCards() {
            const container = document.getElementById('roomStatusCards');
            const totalCountEl = document.getElementById('totalRoomsCount');
            const availableCountEl = document.getElementById('availableRoomsCount');
            
            if (!container || !totalCountEl || !availableCountEl) {
                console.log('会议室状态卡片容器元素未找到');
                return;
            }
            
            console.log('更新会议室状态卡片，rooms数据:', rooms);
            
            if (!rooms || rooms.length === 0) {
                console.log('会议室数据为空，保持占位符显示');
                return;
            }
            
            // 更新统计数据
            const totalRooms = rooms.length;
            const availableRooms = rooms.filter(room => room.status === 'available').length;
            
            totalCountEl.textContent = totalRooms;
            availableCountEl.textContent = availableRooms;
            
            // 清空容器并重新生成
            container.innerHTML = '';
            
            // 生成每个会议室的状态卡片
            rooms.forEach(room => {
                // 检查当前时间是否有会议
                const now = new Date();
                const today = now.toISOString().split('T')[0];
                const currentTime = now.toTimeString().slice(0, 5);
                
                const currentReservation = reservations.find(res => 
                    res.room === room.id && 
                    res.date === today && 
                    currentTime >= res.start && 
                    currentTime < res.end
                );
                
                const isOccupied = currentReservation !== undefined;
                const cardStatus = room.status === 'available' ? (isOccupied ? 'occupied' : 'available') : room.status;
                
                const card = document.createElement('div');
                card.className = `room-status-card ${cardStatus}`;
                
                let statusText, statusClass;
                switch(cardStatus) {
                    case 'available':
                        statusText = '可用';
                        statusClass = 'available';
                        break;
                    case 'occupied':
                        statusText = '使用中';
                        statusClass = 'occupied';
                        break;
                    case 'maintenance':
                        statusText = '维护中';
                        statusClass = 'maintenance';
                        break;
                    default:
                        statusText = '不可用';
                        statusClass = 'maintenance';
                }
                
                card.innerHTML = `
                    <div class="room-card-header">
                        <div class="room-card-name">${room.name}</div>
                        <div class="room-card-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="room-card-info">
                        <div class="room-info-item">
                            <i class="fas fa-users"></i>
                            <span>容量: ${room.capacity}人</span>
                        </div>
                        <div class="room-info-item">
                            <i class="fas fa-tv"></i>
                            <span>设备: ${room.equipment}</span>
                        </div>
                        ${currentReservation ? `
                            <div class="room-info-item" style="color: #f44336; font-weight: 600;">
                                <i class="fas fa-clock"></i>
                                <span>${currentReservation.start}-${currentReservation.end}</span>
                            </div>
                            <div class="room-info-item" style="color: #333; font-weight: 500;">
                                <i class="fas fa-user"></i>
                                <span>${currentReservation.title}</span>
                            </div>
                        ` : ''}
                    </div>
                `;
                
                container.appendChild(card);
            });
        }
        
        // 兼容旧函数名
        function generateRoomStatusCards() {
            updateRoomStatusCards();
        }
        
        // 生成7天预约状态日历
        function generateWeekCalendar() {
            const container = document.getElementById('weekCalendar');
            const today = new Date();
            const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
            
            let calendarHTML = '';
            
            for (let i = 0; i < 7; i++) {
                const date = new Date(today);
                date.setDate(today.getDate() + i);
                
                const dateStr = date.toISOString().split('T')[0];
                const dayName = weekDays[date.getDay()];
                const dayDate = date.getDate();
                
                // 统计当天的会议数量
                const dayMeetings = reservations.filter(res => res.date === dateStr);
                const meetingCount = dayMeetings.length;
                
                const isToday = i === 0;
                const hasMeetings = meetingCount > 0;
                
                let dayClass = 'week-day';
                if (isToday) dayClass += ' today';
                if (hasMeetings) dayClass += ' has-meetings';
                
                calendarHTML += `
                    <div class="${dayClass}">
                        <div class="week-day-name">${dayName}</div>
                        <div class="week-day-date">${dayDate}</div>
                        <div class="week-day-count">${meetingCount}个会议</div>
                    </div>
                `;
            }
            
            container.innerHTML = calendarHTML;
        }
        
        // 加载会议室数据
        async function loadRooms() {
            try {
                const response = await fetch('/api/load_rooms/');
                rooms = await response.json();
            } catch (error) {
                console.error('加载会议室数据失败:', error);
                rooms = [];
            }
        }
        
        // 加载预约数据
        async function loadReservations() {
            try {
                const response = await fetch('/api/load_reservations/');
                reservations = await response.json();
            } catch (error) {
                console.error('加载预约数据失败:', error);
                reservations = [];
            }
        }
        
        // 保存会议室数据
        async function saveRooms() {
            try {
                const response = await fetch('/api/save_rooms/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(rooms)
                });
                const result = await response.json();
                return result.success;
            } catch (error) {
                console.error('保存会议室数据失败:', error);
                return false;
            }
        }
        
        // 保存预约数据
        async function saveReservations() {
            try {
                const response = await fetch('/api/save_reservations/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(reservations)
                });
                const result = await response.json();
                return result.success;
            } catch (error) {
                console.error('保存预约数据失败:', error);
                return false;
            }
        }
        
        // 创建默认会议室数据
        async function createDefaultRooms() {
            const defaultRooms = [
                {
                    id: 'room1',
                    name: '会议室A',
                    capacity: 10,
                    description: '小型会议室，适合团队讨论',
                    equipment: '投影仪、白板、电话会议系统',
                    status: 'available'
                },
                {
                    id: 'room2',
                    name: '会议室B',
                    capacity: 20,
                    description: '中型会议室，适合部门会议',
                    equipment: '大屏显示器、音响系统、视频会议设备',
                    status: 'available'
                },
                {
                    id: 'room3',
                    name: '会议室C',
                    capacity: 50,
                    description: '大型会议室，适合全员会议',
                    equipment: '舞台、音响系统、多媒体设备、同声传译',
                    status: 'available'
                },
                {
                    id: 'room4',
                    name: '培训室',
                    capacity: 30,
                    description: '培训专用教室',
                    equipment: '投影仪、音响、可移动桌椅',
                    status: 'available'
                }
            ];
            
            try {
                const response = await fetch('/api/save_rooms/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(defaultRooms)
                });
                const result = await response.json();
                if (result.success) {
                    console.log('默认会议室数据创建成功');
                } else {
                    console.error('默认会议室数据创建失败');
                }
                return result.success;
            } catch (error) {
                console.error('创建默认会议室数据时出错:', error);
                return false;
            }
        }
        
        // 获取CSRF Token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // 切换显示区域
        function showSection(section) {
            // 隐藏所有区域
            document.getElementById('bookingSection').style.display = 'none';
            document.getElementById('managementSection').style.display = 'none';
            document.getElementById('roomsSection').style.display = 'none';
            
            // 移除所有按钮的active类
            document.querySelectorAll('.switch-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的区域并设置对应按钮为active
            if (section === 'booking') {
                document.getElementById('bookingSection').style.display = 'block';
                // 找到预约会议按钮并设置为active
                const bookingBtn = document.querySelector('.switch-btn[onclick="showBookingSection()"]');
                if (bookingBtn) bookingBtn.classList.add('active');
                
                // 使用更准确的移动设备检测方法
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                                 (window.innerWidth <= 1024 && 'ontouchstart' in window);
                
                // 只在桌面端且非触摸设备时自动滚动到新建预约位置
                if (!isMobile) {
                    setTimeout(() => {
                        const bookingFormCard = document.getElementById('bookingFormCard');
                        if (bookingFormCard) {
                            bookingFormCard.scrollIntoView({ behavior: 'smooth' });
                        }
                    }, 100);
                }
            } else if (section === 'management') {
                document.getElementById('managementSection').style.display = 'block';
                // 找到预约管理按钮并设置为active
                const managementBtn = document.querySelector('.switch-btn[onclick="showSection(\'management\')"]');
                if (managementBtn) managementBtn.classList.add('active');
                displayManagement();
            } else if (section === 'rooms') {
                document.getElementById('roomsSection').style.display = 'block';
                // 找到会议室管理按钮并设置为active
                const roomsBtn = document.querySelector('.switch-btn[onclick="showRoomStatus()"]');
                if (roomsBtn) roomsBtn.classList.add('active');
                displayRooms();
            }
            
            // 切换区域后滚动到页面顶部（除了booking区域，它会滚动到新建预约位置）
            if (section !== 'booking') {
                window.scrollTo({top: 0, behavior: 'smooth'});
            }
        }
        
        // 专门处理新建预约按钮点击的函数
        function showBookingSection() {
            // 调用原有的showSection函数
            showSection('booking');
            
            // 在移动端也滚动到表单位置
            setTimeout(() => {
                const bookingFormCard = document.getElementById('bookingFormCard');
                if (bookingFormCard) {
                    bookingFormCard.scrollIntoView({ behavior: 'smooth' });
                }
            }, 100);
        }
        
        // 生成会议室选项
        function generateRoomOptions() {
            const roomSelect = document.getElementById('meetingRoom');
            roomSelect.innerHTML = '<option value="">-- 请选择会议室 --</option>';
            
            rooms.forEach(room => {
                if (room.status === 'available') {
                    const option = document.createElement('option');
                    option.value = room.id;
                    option.textContent = `${room.name} (${room.capacity}人)`;
                    roomSelect.appendChild(option);
                }
            });
        }
        
        // 填充筛选器选项
        function populateFilterOptions() {
            const roomFilter = document.getElementById('filterRoom');
            const dateFilter = document.getElementById('filterDate');
            
            // 清空现有选项
            roomFilter.innerHTML = '<option value="">所有会议室</option>';
            dateFilter.innerHTML = '<option value="">所有日期</option><option value="today">今天</option><option value="tomorrow">明天</option><option value="week">本周</option>';
            
            // 添加会议室选项
            rooms.forEach(room => {
                const option = document.createElement('option');
                option.value = room.id;
                option.textContent = room.name;
                roomFilter.appendChild(option);
            });
        }
        
        // 更新日期时间
        function updateDateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false });
            const dateStr = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
            
            document.getElementById('currentTime').textContent = timeStr;
            document.getElementById('currentDate').textContent = dateStr;
        }
        
        // 显示预约列表
        function displayReservations() {
            const container = document.getElementById('reservationsContainer');
            container.innerHTML = '';
            const today = new Date().toISOString().split('T')[0];
            const todayReservations = reservations.filter(res => res.date === today);

            todayReservations.sort((a, b) => a.start.localeCompare(b.start));

            // 添加标题行
            const headerRow = document.createElement('div');
            headerRow.className = 'today-reservation-row';
            headerRow.style.fontWeight = 'bold';
            headerRow.style.backgroundColor = '#f8f9fa';
            headerRow.style.borderBottom = '2px solid #dee2e6';
            headerRow.innerHTML = `
                <div class="today-reservation-time" style="text-align: center;">时间</div>
                <div class="today-reservation-title" style="text-align: center;">主题</div>
                <div class="today-reservation-room" style="text-align: center;">会议室</div>
                <div class="today-reservation-booker">预约人</div>
                <div class="today-reservation-department">预约部门</div>
            `;
            container.appendChild(headerRow);
            
            if (todayReservations.length === 0) {
                const noDataRow = document.createElement('div');
                noDataRow.className = 'today-reservation-row';
                noDataRow.innerHTML = '<div colspan="4" style="text-align: center; color: #666; grid-column: 1 / -1;">今天暂无会议安排</div>';
                container.appendChild(noDataRow);
                return;
            }
            
            todayReservations.forEach(res => {
                const room = rooms.find(r => r.id === res.room);
                const status = getReservationStatus(res.start, res.end);
                const statusClass = 
                    status === 'ongoing' ? 'status-ongoing' :
                    status === 'upcoming' ? 'status-upcoming' : 'status-ended';

                const row = document.createElement('div');
                row.className = 'today-reservation-row';
                row.innerHTML = `
                    <div class="today-reservation-time">
                        <span class="status-indicator ${statusClass}"></span>
                        ${res.start} - ${res.end}
                    </div>
                    <div class="today-reservation-title">${res.title}</div>
                    <div class="today-reservation-room">${room ? room.name : '未知会议室'}</div>
                    <div class="today-reservation-booker">${res.booker}</div>
                    <div class="today-reservation-department">${res.department || '未指定'}</div>
                `;
                container.appendChild(row);
            });
        }
        
        // 获取预约状态
        function getReservationStatus(startTime, endTime) {
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentTime = now.toTimeString().slice(0, 5);
            
            const [startHour, startMinute] = startTime.split(':').map(Number);
            const [endHour, endMinute] = endTime.split(':').map(Number);
            
            const startDate = new Date();
            startDate.setHours(startHour, startMinute, 0, 0);
            const endDate = new Date();
            endDate.setHours(endHour, endMinute, 0, 0);
            
            if (now < startDate) return 'upcoming';
            if (now >= startDate && now <= endDate) return 'ongoing';
            return 'ended';
        }
        
        // 显示预约管理列表
        function displayManagement() {
            // 重置筛选器
            document.getElementById('filterRoom').value = '';
            document.getElementById('filterDate').value = '';
            document.getElementById('searchBooker').value = '';
            
            // 显示所有预约
            displayFilteredReservations();
            
            // 更新统计数据
            updateReservationStats();
        }
        
        // 显示会议室列表
        function displayRooms() {
            const container = document.getElementById('roomsContainer');
            
            if (rooms.length === 0) {
                container.innerHTML = '<div class="no-items"><i class="fas fa-door-closed"></i><p>暂无会议室</p></div>';
                return;
            }
            
            container.innerHTML = rooms.map(room => `
                <div class="item">
                    <div data-label="名称">${room.name}</div>
                    <div data-label="容量">${room.capacity}人</div>
                    <div data-label="设备">${room.equipment || '无'}</div>
                    <div data-label="状态">${getStatusText(room.status)}</div>
                    <div class="item-actions">
                        <button class="action-btn btn-edit" onclick="editRoom('${room.id}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="action-btn btn-delete" onclick="deleteRoom('${room.id}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'available': '可用',
                'maintenance': '维护中',
                'unavailable': '不可用'
            };
            return statusMap[status] || status;
        }
        
        // 更新当前事件
        function updateCurrentEvent() {
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentTime = now.toTimeString().slice(0, 5);
            
            const currentReservation = reservations.find(res => {
                return res.date === today && res.start <= currentTime && res.end > currentTime;
            });
            
            const currentEventTitle = document.getElementById('currentEventTitle');
            const currentEventDetails = document.getElementById('currentEventDetails');
            
            if (currentReservation) {
                const room = rooms.find(r => r.id === currentReservation.room);
                currentEventTitle.textContent = currentReservation.title;
                currentEventDetails.innerHTML = `
                    <div>时间: ${currentReservation.start} - ${currentReservation.end}</div>
                    <div>会议室: ${room ? room.name : '未知'}</div>
                    <div>预约人: ${currentReservation.booker}</div>
                    <div>预约部门: ${currentReservation.department || '未指定'}</div>
                `;
            } else {
                currentEventTitle.textContent = '暂无进行中的会议';
                currentEventDetails.innerHTML = '<div>会议室空闲中</div>';
            }
            
            // 更新下一个会议
            const nextReservation = reservations
                .filter(res => {
                    const resDateTime = new Date(res.date + ' ' + res.start);
                    return resDateTime > now;
                })
                .sort((a, b) => {
                    const dateA = new Date(a.date + ' ' + a.start);
                    const dateB = new Date(b.date + ' ' + b.start);
                    return dateA - dateB;
                })[0];
            
            const nextEventDetails = document.getElementById('nextEventDetails');
            
            if (nextReservation) {
                const room = rooms.find(r => r.id === nextReservation.room);
                nextEventDetails.innerHTML = `
                    <div>会议: ${nextReservation.title}</div>
                    <div>时间: ${nextReservation.date} ${nextReservation.start} - ${nextReservation.end}</div>
                    <div>会议室: ${room ? room.name : '未知'}</div>
                    <div>预约人: ${nextReservation.booker}</div>
                    <div>预约部门: ${nextReservation.department || '未指定'}</div>
                `;
            } else {
                nextEventDetails.innerHTML = '<div>暂无后续会议</div>';
            }
        }
        
        // 更新预约统计数据（支持筛选）
        function updateReservationStats() {
            console.log('更新统计数据，当前预约数组:', reservations);
            
            const filteredReservations = getFilteredReservations();
            
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentTime = now.toTimeString().slice(0, 5);
            
            // 总预约数（根据筛选条件）
            const totalReservations = filteredReservations.length;
            console.log('筛选后总预约数:', totalReservations);
            document.getElementById('totalReservations').textContent = totalReservations;
            
            // 今日预约数（根据筛选条件）
            const todayReservations = filteredReservations.filter(res => res.date === today).length;
            console.log('筛选后今日预约数:', todayReservations, '今天日期:', today);
            document.getElementById('todayReservations').textContent = todayReservations;
            
            // 即将到来的预约数（未来24小时内，根据筛选条件）
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowStr = tomorrow.toISOString().split('T')[0];
            
            const upcomingReservations = filteredReservations.filter(res => {
                // 今天剩余的预约
                if (res.date === today && res.start > currentTime) {
                    return true;
                }
                // 明天的预约
                if (res.date === tomorrowStr) {
                    return true;
                }
                return false;
            }).length;
            
            console.log('筛选后即将到来的预约数:', upcomingReservations);
            document.getElementById('upcomingReservations').textContent = upcomingReservations;
        }
        
        // 检查时间冲突
        function checkTimeConflict(roomId, date, startTime, endTime, excludeId = null) {
            return reservations.some(res => {
                if (excludeId && res.id === excludeId) return false;
                if (res.room !== roomId || res.date !== date) return false;
                
                return (startTime < res.end && endTime > res.start);
            });
        }
        
        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess() {
            const confirmDiv = document.getElementById('confirmation');
            confirmDiv.style.display = 'block';
            setTimeout(() => {
                confirmDiv.style.display = 'none';
            }, 3000);
        }
        
        // 预约表单提交
        document.getElementById('bookingForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const roomId = document.getElementById('meetingRoom').value;
            const date = document.getElementById('bookingDate').value;
            const startOption = document.querySelector('#startTimeSelector .time-option.selected');
            const endOption = document.querySelector('#endTimeSelector .time-option.selected');
            const title = document.getElementById('meetingTitle').value;
            const booker = document.getElementById('bookerName').value;
            const department = document.getElementById('bookerDepartment').value;
            
            // 验证
            if (!roomId) {
                showError('请选择会议室');
                return;
            }
            
            if (!startOption || !endOption) {
                showError('请选择开始时间和结束时间');
                return;
            }
            
            const startTime = startOption.dataset.time;
            const endTime = endOption.dataset.time;
            
            if (!date || !title || !booker) {
                showError('请填写所有必填字段');
                return;
            }
            
            if (startTime >= endTime) {
                const timeError = document.getElementById('timeError');
                if (timeError) timeError.style.display = 'block';
                return;
            }
            
            // 检查时间冲突
            const currentReservations = getReservationsForCurrentRoomAndDate();
            const conflict = checkTimeConflict(startTime, endTime, currentReservations);
            
            if (conflict) {
                const conflictMessage = document.getElementById('conflictMessage');
                const conflictError = document.getElementById('conflictError');
                if (conflictMessage && conflictError) {
                    conflictMessage.textContent = 
                        `该时间段与 "${conflict.title}" 会议冲突 (${conflict.start}-${conflict.end})`;
                    conflictError.style.display = 'block';
                }
                return;
            }
            
            // 添加预约
            const newReservation = {
                id: Date.now(),
                room: roomId,
                date: date,
                start: startTime,
                end: endTime,
                title: title,
                booker: booker,
                department: department
            };
            
            reservations.push(newReservation);
            
            // 保存到后端
            const success = await saveReservations();
            if (success) {
                showSuccess();
                this.reset();
                setBookingDateRange();
                generateTimeOptions();
                displayReservations();
                updateCurrentEvent();
                generateWeekCalendar();
                generateRoomStatusCards();
                updateRoomAvailability();
                updateReservationStats();
                
                // 重置时间选择器显示
                const startDisplay = document.getElementById('startTimeDisplay');
                const endDisplay = document.getElementById('endTimeDisplay');
                if (startDisplay) startDisplay.textContent = '--:--';
                if (endDisplay) endDisplay.textContent = '--:--';
                
                // 隐藏错误信息
                const timeError = document.getElementById('timeError');
                const conflictError = document.getElementById('conflictError');
                if (timeError) timeError.style.display = 'none';
                if (conflictError) conflictError.style.display = 'none';
            } else {
                showError('保存失败，请重试');
            }
        });
        
        // 删除预约
        async function deleteReservation(id) {
            if (confirm('确定要删除这个预约吗？')) {
                reservations = reservations.filter(res => res.id !== id);
                const success = await saveReservations();
                if (success) {
                    displayManagement();
                    displayReservations();
                    updateCurrentEvent();
                    generateWeekCalendar();
                    generateRoomStatusCards();
                    updateRoomAvailability();
                    updateReservationStats();
                } else {
                    showError('删除失败，请重试');
                }
            }
        }
        
        // 添加会议室按钮
        document.getElementById('addRoomBtn').addEventListener('click', function() {
            currentEditingRoom = null;
            document.getElementById('roomFormTitle').textContent = '添加会议室';
            document.getElementById('roomForm').reset();
            document.getElementById('roomId').value = '';
            document.getElementById('roomFormContainer').style.display = 'block';
        });
        
        // 取消会议室表单
        document.getElementById('cancelRoomForm').addEventListener('click', function() {
            document.getElementById('roomFormContainer').style.display = 'none';
        });
        
        // 编辑会议室
        function editRoom(roomId) {
            const room = rooms.find(r => r.id === roomId);
            if (!room) return;
            
            currentEditingRoom = roomId;
            document.getElementById('roomFormTitle').textContent = '编辑会议室';
            document.getElementById('roomId').value = room.id;
            document.getElementById('roomName').value = room.name;
            document.getElementById('roomCapacity').value = room.capacity;
            document.getElementById('roomDescription').value = room.description || '';
            document.getElementById('roomEquipment').value = room.equipment || '';
            document.getElementById('roomStatus').value = room.status;
            document.getElementById('roomFormContainer').style.display = 'block';
        }
        
        // 删除会议室
        async function deleteRoom(roomId) {
            if (confirm('确定要删除这个会议室吗？相关预约也将被删除。')) {
                rooms = rooms.filter(r => r.id !== roomId);
                reservations = reservations.filter(res => res.room !== roomId);
                
                const roomSuccess = await saveRooms();
                const resSuccess = await saveReservations();
                
                if (roomSuccess && resSuccess) {
                    displayRooms();
                    generateRoomOptions();
                    displayReservations();
                    updateCurrentEvent();
                } else {
                    showError('删除失败，请重试');
                }
            }
        }
        
        // 会议室表单提交
        document.getElementById('roomForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const name = document.getElementById('roomName').value;
            const capacity = parseInt(document.getElementById('roomCapacity').value);
            const description = document.getElementById('roomDescription').value;
            const equipment = document.getElementById('roomEquipment').value;
            const status = document.getElementById('roomStatus').value;
            
            if (!name || !capacity) {
                showError('请填写会议室名称和容量');
                return;
            }
            
            let isEdit = false;
            if (currentEditingRoom) {
                // 编辑现有会议室
                const roomIndex = rooms.findIndex(r => r.id === currentEditingRoom);
                if (roomIndex !== -1) {
                    rooms[roomIndex] = {
                        id: currentEditingRoom,
                        name: name,
                        capacity: capacity,
                        description: description,
                        equipment: equipment,
                        status: status
                    };
                    isEdit = true;
                }
            } else {
                // 添加新会议室
                const newRoom = {
                    id: 'room' + Date.now(),
                    name: name,
                    capacity: capacity,
                    description: description,
                    equipment: equipment,
                    status: status
                };
                rooms.push(newRoom);
            }
            
            const success = await saveRooms();
            if (success) {
                // 显示成功提示
                const message = isEdit ? '会议室更新成功！' : '会议室添加成功！';
                alert(message);
                
                document.getElementById('roomFormContainer').style.display = 'none';
                displayRooms();
                generateRoomOptions();
                
                // 重新加载数据确保同步
                await loadRooms();
                displayRooms();
                generateRoomOptions();
            } else {
                showError('保存失败，请重试');
            }
        });
        
        // 定期更新当前事件
        setInterval(updateCurrentEvent, 60000); // 每分钟更新一次
        
        // 预约管理相关函数
        let currentEditingReservation = null;
        
        // 添加预约按钮
        document.getElementById('addReservationBtn').addEventListener('click', function() {
            currentEditingReservation = null;
            document.getElementById('reservationFormTitle').textContent = '添加预约';
            document.getElementById('reservationForm').reset();
            document.getElementById('reservationId').value = '';
            generateEditRoomOptions();
            setEditBookingDateRange();
            document.getElementById('reservationFormContainer').style.display = 'block';
        });
        
        // 取消预约表单
        document.getElementById('cancelReservationForm').addEventListener('click', function() {
            document.getElementById('reservationFormContainer').style.display = 'none';
        });
        
        // 编辑预约
        function editReservation(reservationId) {
            const reservation = reservations.find(r => r.id === reservationId);
            if (!reservation) return;
            
            currentEditingReservation = reservationId;
            document.getElementById('reservationFormTitle').textContent = '编辑预约';
            document.getElementById('reservationId').value = reservation.id;
            document.getElementById('editMeetingRoom').value = reservation.room;
            document.getElementById('editBookingDate').value = reservation.date;
            document.getElementById('editStartTime').value = reservation.start;
            document.getElementById('editEndTime').value = reservation.end;
            document.getElementById('editMeetingTitle').value = reservation.title;
            document.getElementById('editBookerName').value = reservation.booker;
            document.getElementById('editBookerDepartment').value = reservation.department || '';
            generateEditRoomOptions();
            setEditBookingDateRange();
            document.getElementById('reservationFormContainer').style.display = 'block';
        }
        
        // 生成编辑表单的会议室选项
        function generateEditRoomOptions() {
            const roomSelect = document.getElementById('editMeetingRoom');
            roomSelect.innerHTML = '<option value="">-- 请选择会议室 --</option>';
            
            rooms.forEach(room => {
                const option = document.createElement('option');
                option.value = room.id;
                option.textContent = `${room.name} (${room.capacity}人)`;
                roomSelect.appendChild(option);
            });
        }
        
        // 设置编辑表单的预约日期范围
        function setEditBookingDateRange() {
            const today = new Date();
            const dateInput = document.getElementById('editBookingDate');
            if (!dateInput.value) {
                dateInput.valueAsDate = today;
            }
            dateInput.min = today.toISOString().split('T')[0];
            const maxDate = new Date();
            maxDate.setDate(today.getDate() + 30); // 允许预约30天内
            dateInput.max = maxDate.toISOString().split('T')[0];
        }
        
        // 预约表单提交
        document.getElementById('reservationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const roomId = document.getElementById('editMeetingRoom').value;
            const date = document.getElementById('editBookingDate').value;
            const startTime = document.getElementById('editStartTime').value;
            const endTime = document.getElementById('editEndTime').value;
            const title = document.getElementById('editMeetingTitle').value;
            const booker = document.getElementById('editBookerName').value;
            const department = document.getElementById('editBookerDepartment').value;
            
            // 验证
            if (!roomId || !date || !startTime || !endTime || !title || !booker) {
                showError('请填写所有必填字段');
                return;
            }
            
            if (startTime >= endTime) {
                showError('结束时间必须晚于开始时间');
                return;
            }
            
            if (checkTimeConflict(roomId, date, startTime, endTime, currentEditingReservation)) {
                showError('该时间段已被预约，请选择其他时间');
                return;
            }
            
            let isEdit = false;
            if (currentEditingReservation) {
                // 编辑现有预约
                const reservationIndex = reservations.findIndex(r => r.id === currentEditingReservation);
                if (reservationIndex !== -1) {
                    reservations[reservationIndex] = {
                        id: currentEditingReservation,
                        room: roomId,
                        date: date,
                        start: startTime,
                        end: endTime,
                        title: title,
                        booker: booker,
                        department: department
                    };
                    isEdit = true;
                }
            } else {
                // 添加新预约
                const newReservation = {
                    id: Date.now(),
                    room: roomId,
                    date: date,
                    start: startTime,
                    end: endTime,
                    title: title,
                    booker: booker,
                    department: department
                };
                reservations.push(newReservation);
            }
            
            // 保存到后端
            const success = await saveReservations();
            if (success) {
                const message = isEdit ? '预约更新成功！' : '预约添加成功！';
                alert(message);
                
                document.getElementById('reservationFormContainer').style.display = 'none';
                displayManagement();
                displayReservations();
                updateCurrentEvent();
                generateWeekCalendar();
                generateRoomStatusCards();
                
                // 重新加载数据确保同步
                await loadReservations();
                displayManagement();
                displayReservations();
                updateCurrentEvent();
                generateWeekCalendar();
                generateRoomStatusCards();
                updateAvailableTimeSlots();
                updateReservationStats();
            } else {
                showError('保存失败，请重试');
            }
        });
        
        // 密码验证相关函数
        function checkPassword(section) {
            pendingSection = section;
            const passwordModal = document.getElementById('passwordModal');
            if (passwordModal) {
                passwordModal.style.display = 'block';
                const passwordInput = document.getElementById('passwordInput');
                if (passwordInput) {
                    passwordInput.value = ''; // 清空密码输入框
                    passwordInput.focus();
                }
            } else {
                console.error('密码验证对话框不存在');
                // 如果对话框不存在，直接跳转
                showSection(section);
            }
        }
        
        function verifyPassword() {
            const password = document.getElementById('passwordInput').value;
            if (password === '666888') {
                closePasswordModal();
                showSection(pendingSection);
            } else {
                alert('密码错误，请重新输入！');
                document.getElementById('passwordInput').value = '';
                document.getElementById('passwordInput').focus();
            }
        }
        
        function closePasswordModal() {
            const passwordModal = document.getElementById('passwordModal');
            if (passwordModal) {
                passwordModal.style.display = 'none';
                const passwordInput = document.getElementById('passwordInput');
                if (passwordInput) {
                    passwordInput.value = '';
                }
            }
            pendingSection = null;
        }
        
        // 密码输入框回车事件
        document.getElementById('passwordInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                verifyPassword();
            }
        });
        
        // 跳转到管理后台
        function goToAdmin() {
            window.location.href = '/admin/';
        }
        
        // 会议室情况日历相关函数
        function showRoomStatus() {
            window.location.href = '/room_status/';
        }
        
        function closeCalendarModal() {
            document.getElementById('calendarModal').style.display = 'none';
        }
        
        function changeMonth(direction) {
            currentCalendarDate.setMonth(currentCalendarDate.getMonth() + direction);
            generateCalendar();
        }
        
        function generateCalendar() {
            const year = currentCalendarDate.getFullYear();
            const month = currentCalendarDate.getMonth();
            
            // 更新标题
            document.getElementById('calendarTitle').textContent = `${year}年${month + 1}月`;
            
            // 获取月份信息
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());
            
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];
            
            let calendarHTML = '';
            
            // 添加星期标题
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            weekdays.forEach(day => {
                calendarHTML += `<div class="calendar-day-header">${day}</div>`;
            });
            
            // 生成日历格子
            for (let i = 0; i < 42; i++) {
                const currentDate = new Date(startDate);
                currentDate.setDate(startDate.getDate() + i);
                
                const dateStr = currentDate.toISOString().split('T')[0];
                const dayNumber = currentDate.getDate();
                const isCurrentMonth = currentDate.getMonth() === month;
                const isToday = dateStr === todayStr;
                
                let dayClass = 'calendar-day';
                if (!isCurrentMonth) dayClass += ' other-month';
                if (isToday) dayClass += ' today';
                
                // 获取当天的会议
                const dayReservations = reservations.filter(res => res.date === dateStr);
                
                let eventsHTML = '';
                dayReservations.forEach(res => {
                    const room = rooms.find(r => r.id === res.room);
                    const roomName = room ? room.name : '未知';
                    const department = res.department ? ` - ${res.department}` : '';
                    eventsHTML += `<div class="calendar-event" title="${res.title} - ${roomName} - ${res.booker}${department} (${res.start}-${res.end})">${res.start} ${res.title}</div>`;
                });
                
                calendarHTML += `
                    <div class="${dayClass}">
                        <div class="calendar-day-number">${dayNumber}</div>
                        ${eventsHTML}
                    </div>
                `;
            }
            
            document.getElementById('calendarGrid').innerHTML = calendarHTML;
        }
        

        
        // 筛选预约数据显示
        function displayFilteredReservations() {
            const filteredReservations = getFilteredReservations();
            
            // 按日期和时间排序
            filteredReservations.sort((a, b) => {
                if (a.date !== b.date) {
                    return new Date(a.date) - new Date(b.date);
                }
                return a.start.localeCompare(b.start);
            });
            
            // 显示筛选后的结果
            displayFilteredManagement(filteredReservations);
        }
        
        // 显示筛选后的预约管理列表
        function displayFilteredManagement(filteredReservations) {
            const container = document.getElementById('managementContainer');
            
            if (filteredReservations.length === 0) {
                container.innerHTML = '<div class="no-items"><i class="fas fa-calendar-times"></i><p>暂无符合条件的预约记录</p></div>';
                return;
            }
            
            container.innerHTML = filteredReservations.map(res => {
                const room = rooms.find(r => r.id === res.room);
                return `
                    <div class="reservation-item">
                        <div data-label="会议室">${room ? room.name : '未知会议室'}</div>
                        <div data-label="日期">${res.date}</div>
                        <div data-label="时间">${res.start} - ${res.end}</div>
                        <div data-label="会议主题">${res.title}</div>
                        <div data-label="预约人">${res.booker}</div>
                        <div data-label="预约部门">${res.department || '未指定'}</div>
                        <div class="reservation-actions">
                             <button class="action-btn btn-edit" onclick="editReservation(${res.id})">
                                 <i class="fas fa-edit"></i> 编辑
                             </button>
                             <button class="action-btn btn-delete" onclick="deleteReservation(${res.id})">
                                 <i class="fas fa-trash"></i> 删除
                             </button>
                         </div>
                    </div>
                `;
            }).join('');
        }
        
        // 获取当前筛选后的预约数据
        function getFilteredReservations() {
            const roomFilter = document.getElementById('filterRoom').value;
            const dateFilter = document.getElementById('filterDate').value;
            const searchTerm = document.getElementById('searchBooker').value.toLowerCase();
            
            let filteredReservations = [...reservations];
            
            // 按会议室筛选
            if (roomFilter) {
                filteredReservations = filteredReservations.filter(res => res.room === roomFilter);
            }
            
            // 按日期筛选
            if (dateFilter) {
                const today = new Date().toISOString().split('T')[0];
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                const tomorrowStr = tomorrow.toISOString().split('T')[0];
                
                switch(dateFilter) {
                    case 'today':
                        filteredReservations = filteredReservations.filter(res => res.date === today);
                        break;
                    case 'tomorrow':
                        filteredReservations = filteredReservations.filter(res => res.date === tomorrowStr);
                        break;
                    case 'week':
                        const weekStart = new Date();
                        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
                        const weekEnd = new Date(weekStart);
                        weekEnd.setDate(weekStart.getDate() + 6);
                        filteredReservations = filteredReservations.filter(res => {
                            const resDate = new Date(res.date);
                            return resDate >= weekStart && resDate <= weekEnd;
                        });
                        break;
                }
            }
            
            // 按预约人搜索
            if (searchTerm) {
                filteredReservations = filteredReservations.filter(res => 
                    res.booker.toLowerCase().includes(searchTerm)
                );
            }
            
            return filteredReservations;
        }
        
        // 导出预约数据为CSV
        function exportReservationsToCSV() {
            const dataToExport = getFilteredReservations();
            
            if (dataToExport.length === 0) {
                alert('暂无符合筛选条件的预约数据可导出');
                return;
            }
            
            // CSV标题行
            const headers = ['会议室', '日期', '开始时间', '结束时间', '会议主题', '预约人', '预约部门'];
            
            // 构建CSV内容
            let csvContent = headers.join(',') + '\n';
            
            dataToExport.forEach(res => {
                const room = rooms.find(r => r.id === res.room);
                const row = [
                    room ? room.name : '未知会议室',
                    res.date,
                    res.start,
                    res.end,
                    `"${res.title}"`, // 用引号包围以防止逗号问题
                    `"${res.booker}"`,
                    `"${res.department || '未指定'}"`
                ];
                csvContent += row.join(',') + '\n';
            });
            
            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `会议室预约数据_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                alert('您的浏览器不支持文件下载功能');
            }
        }
        
        // 筛选器改变时的处理函数
        function handleFilterChange() {
            displayFilteredReservations();
            updateReservationStats();
        }
        
        // 添加筛选器事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const filterRoom = document.getElementById('filterRoom');
            const filterDate = document.getElementById('filterDate');
            const searchBooker = document.getElementById('searchBooker');
            const exportBtn = document.getElementById('exportReservationsBtn');
            
            if (filterRoom) {
                filterRoom.addEventListener('change', handleFilterChange);
            }
            if (filterDate) {
                filterDate.addEventListener('change', handleFilterChange);
            }
            if (searchBooker) {
                searchBooker.addEventListener('input', handleFilterChange);
            }
            if (exportBtn) {
                exportBtn.addEventListener('click', exportReservationsToCSV);
            }
            
            // 确保页面加载完成后，如果显示的是管理页面，也会更新统计数据
            setTimeout(() => {
                const managementSection = document.getElementById('managementSection');
                if (managementSection && managementSection.style.display !== 'none') {
                    updateReservationStats();
                }
            }, 200);
        });
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const passwordModal = document.getElementById('passwordModal');
            const calendarModal = document.getElementById('calendarModal');
            
            if (event.target === passwordModal) {
                closePasswordModal();
            }
            if (event.target === calendarModal) {
                closeCalendarModal();
            }
        }
    </script>
</body>
</html>