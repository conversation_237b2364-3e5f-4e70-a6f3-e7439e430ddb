# Generated by Django 4.2.23 on 2025-07-15 05:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Room',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='会议室名称')),
                ('capacity', models.IntegerField(verbose_name='容量')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('equipment', models.CharField(blank=True, max_length=255, null=True, verbose_name='设备')),
                ('status', models.CharField(choices=[('available', '可用'), ('maintenance', '维护中'), ('unavailable', '不可用')], default='available', max_length=20, verbose_name='状态')),
            ],
            options={
                'verbose_name': '会议室',
                'verbose_name_plural': '会议室',
            },
        ),
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='键')),
                ('value', models.TextField(blank=True, null=True, verbose_name='值')),
            ],
            options={
                'verbose_name': '系统设置',
                'verbose_name_plural': '系统设置',
            },
        ),
        migrations.CreateModel(
            name='Reservation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='日期')),
                ('start_time', models.TimeField(verbose_name='开始时间')),
                ('end_time', models.TimeField(verbose_name='结束时间')),
                ('title', models.CharField(max_length=255, verbose_name='会议主题')),
                ('booker', models.CharField(max_length=100, verbose_name='预约人')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='预约部门')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='booking.room', verbose_name='会议室')),
            ],
            options={
                'verbose_name': '预约记录',
                'verbose_name_plural': '预约记录',
            },
        ),
    ]
