<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - Etek会议室预约系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #4a5568;
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .admin-nav {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .nav-btn {
            background: #e2e8f0;
            color: #4a5568;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-btn:hover {
            background: #cbd5e0;
            transform: translateY(-2px);
        }
        
        .nav-btn.active {
            background: #667eea;
            color: white;
        }
        
        .content-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .section-title {
            font-size: 1.5rem;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #a0aec0;
        }
        
        .btn-secondary:hover {
            background: #718096;
        }
        
        .item-list {
            background: #f7fafc;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .item {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 15px;
            padding: 15px 20px;
            border-bottom: 1px solid #e2e8f0;
            align-items: center;
        }
        
        .item-header {
            background: #4a5568;
            color: white;
            font-weight: bold;
        }
        
        .item:last-child {
            border-bottom: none;
        }
        
        .item:nth-child(even) {
            background: white;
        }
        
        .item-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
        }
        
        .btn-edit {
            background: #38b2ac;
        }
        
        .btn-edit:hover {
            background: #319795;
        }
        
        .btn-delete {
            background: #e53e3e;
        }
        
        .btn-delete:hover {
            background: #c53030;
        }
        
        .form-container {
            background: #f7fafc;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
            display: none;
        }
        
        .form-container.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-group label {
            font-weight: 500;
            color: #4a5568;
        }
        
        .form-group input,
        .form-group select {
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }
        
        .no-items {
            text-align: center;
            padding: 40px;
            color: #a0aec0;
        }
        
        .no-items i {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 12px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        
        .success-message {
            background: #c6f6d5;
            color: #2f855a;
            padding: 12px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .admin-nav {
                flex-direction: column;
            }
            
            .nav-btn {
                justify-content: center;
            }
            
            .item {
                grid-template-columns: 1fr;
                gap: 10px;
                text-align: center;
            }
            
            .item-actions {
                justify-content: center;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-cogs"></i>
                管理后台
            </h1>
            <a href="/" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                返回主页
            </a>
        </div>
        
        <div class="admin-nav">
            <button class="nav-btn active" onclick="showSection('management')">
                <i class="fas fa-list-alt"></i>
                预约管理
            </button>
            <button class="nav-btn" onclick="showSection('rooms')">
                <i class="fas fa-door-open"></i>
                会议室管理
            </button>
        </div>
        
        <!-- 预约管理界面 -->
        <div id="managementSection" class="content-section active">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-list-alt"></i>
                    预约管理
                </h2>
            </div>
            
            <div class="item-list">
                <div class="item item-header">
                    <div>会议主题</div>
                    <div>会议室</div>
                    <div>日期</div>
                    <div>时间</div>
                    <div>操作</div>
                </div>
                <div id="managementContainer">
                    <!-- 管理列表将动态生成 -->
                </div>
            </div>
        </div>
        
        <!-- 会议室管理界面 -->
        <div id="roomsSection" class="content-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-door-open"></i>
                    会议室管理
                </h2>
                <button id="addRoomBtn" class="btn">
                    <i class="fas fa-plus"></i>
                    添加会议室
                </button>
            </div>
            
            <div class="item-list">
                <div class="item item-header">
                    <div>名称</div>
                    <div>容量</div>
                    <div>设备</div>
                    <div>状态</div>
                    <div>操作</div>
                </div>
                <div id="roomsContainer">
                    <!-- 会议室列表将动态生成 -->
                </div>
            </div>
            
            <div id="roomFormContainer" class="form-container">
                <h3 id="roomFormTitle">添加会议室</h3>
                <form id="roomForm">
                    <input type="hidden" id="roomId">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="roomName">会议室名称</label>
                            <input type="text" id="roomName" placeholder="例如：创新厅" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="roomCapacity">最大容量</label>
                            <input type="number" id="roomCapacity" min="1" placeholder="例如：10" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="roomDescription">描述</label>
                            <input type="text" id="roomDescription" placeholder="会议室简要描述">
                        </div>
                        
                        <div class="form-group">
                            <label for="roomEquipment">设备</label>
                            <input type="text" id="roomEquipment" placeholder="例如：投影仪, 白板">
                        </div>
                        
                        <div class="form-group">
                            <label for="roomStatus">状态</label>
                            <select id="roomStatus">
                                <option value="available">可用</option>
                                <option value="maintenance">维护中</option>
                                <option value="unavailable">不可用</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn">
                            <i class="fas fa-save"></i>
                            保存会议室
                        </button>
                        <button type="button" id="cancelRoomForm" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
    </div>
    
    <script>
        // 全局变量
        let rooms = [];
        let reservations = [];
        let currentEditingRoom = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });
        
        // 初始化页面
        async function initializePage() {
            await loadRooms();
            await loadReservations();
            displayManagement();
            displayRooms();
        }
        
        // 加载会议室数据
        async function loadRooms() {
            try {
                const response = await fetch('/api/load_rooms/');
                rooms = await response.json();
            } catch (error) {
                console.error('加载会议室数据失败:', error);
                rooms = [];
            }
        }
        
        // 加载预约数据
        async function loadReservations() {
            try {
                const response = await fetch('/api/load_reservations/');
                reservations = await response.json();
            } catch (error) {
                console.error('加载预约数据失败:', error);
                reservations = [];
            }
        }
        
        // 保存会议室数据
        async function saveRooms() {
            try {
                const response = await fetch('/api/save_rooms/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(rooms)
                });
                const result = await response.json();
                return result.success;
            } catch (error) {
                console.error('保存会议室数据失败:', error);
                return false;
            }
        }
        
        // 保存预约数据
        async function saveReservations() {
            try {
                const response = await fetch('/api/save_reservations/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(reservations)
                });
                const result = await response.json();
                return result.success;
            } catch (error) {
                console.error('保存预约数据失败:', error);
                return false;
            }
        }
        
        // 获取CSRF Token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // 返回主页
        function goBack() {
            window.location.href = '/';
        }
        
        // 切换显示区域
        function showSection(section) {
            // 隐藏所有区域
            document.querySelectorAll('.content-section').forEach(sec => {
                sec.classList.remove('active');
            });
            
            // 移除所有按钮的active类
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的区域
            if (section === 'management') {
                document.getElementById('managementSection').classList.add('active');
                document.querySelector('.nav-btn[onclick="showSection(\'management\')"]').classList.add('active');
                displayManagement();
            } else if (section === 'rooms') {
                document.getElementById('roomsSection').classList.add('active');
                document.querySelector('.nav-btn[onclick="showSection(\'rooms\')"]').classList.add('active');
                displayRooms();
            }
        }
        
        // 显示管理列表
        function displayManagement() {
            const container = document.getElementById('managementContainer');
            
            if (reservations.length === 0) {
                container.innerHTML = '<div class="no-items"><i class="fas fa-calendar-times"></i><p>暂无预约记录</p></div>';
                return;
            }
            
            const sortedReservations = [...reservations].sort((a, b) => {
                const dateA = new Date(a.date + ' ' + a.start);
                const dateB = new Date(b.date + ' ' + b.start);
                return dateB - dateA;
            });
            
            container.innerHTML = sortedReservations.map(res => {
                const room = rooms.find(r => r.id === res.room);
                return `
                    <div class="item">
                        <div>${res.title}</div>
                        <div>${room ? room.name : '未知会议室'}</div>
                        <div>${res.date}</div>
                        <div>${res.start} - ${res.end}</div>
                        <div class="item-actions">
                            <button class="action-btn btn-delete" onclick="deleteReservation(${res.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 显示会议室列表
        function displayRooms() {
            const container = document.getElementById('roomsContainer');
            
            if (rooms.length === 0) {
                container.innerHTML = '<div class="no-items"><i class="fas fa-door-closed"></i><p>暂无会议室</p></div>';
                return;
            }
            
            container.innerHTML = rooms.map(room => `
                <div class="item">
                    <div>${room.name}</div>
                    <div>${room.capacity}人</div>
                    <div>${room.equipment || '无'}</div>
                    <div>${getStatusText(room.status)}</div>
                    <div class="item-actions">
                        <button class="action-btn btn-edit" onclick="editRoom('${room.id}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="action-btn btn-delete" onclick="deleteRoom('${room.id}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'available': '可用',
                'maintenance': '维护中',
                'unavailable': '不可用'
            };
            return statusMap[status] || status;
        }
        
        // 删除预约
        async function deleteReservation(id) {
            if (confirm('确定要删除这个预约吗？')) {
                reservations = reservations.filter(res => res.id !== id);
                const success = await saveReservations();
                if (success) {
                    displayManagement();
                    showSuccess('预约删除成功！');
                } else {
                    showError('删除失败，请重试');
                }
            }
        }
        
        // 添加会议室按钮
        document.getElementById('addRoomBtn').addEventListener('click', function() {
            currentEditingRoom = null;
            document.getElementById('roomFormTitle').textContent = '添加会议室';
            document.getElementById('roomForm').reset();
            document.getElementById('roomId').value = '';
            document.getElementById('roomFormContainer').classList.add('active');
        });
        
        // 取消会议室表单
        document.getElementById('cancelRoomForm').addEventListener('click', function() {
            document.getElementById('roomFormContainer').classList.remove('active');
        });
        
        // 编辑会议室
        function editRoom(roomId) {
            const room = rooms.find(r => r.id === roomId);
            if (!room) return;
            
            currentEditingRoom = roomId;
            document.getElementById('roomFormTitle').textContent = '编辑会议室';
            document.getElementById('roomId').value = room.id;
            document.getElementById('roomName').value = room.name;
            document.getElementById('roomCapacity').value = room.capacity;
            document.getElementById('roomDescription').value = room.description || '';
            document.getElementById('roomEquipment').value = room.equipment || '';
            document.getElementById('roomStatus').value = room.status;
            document.getElementById('roomFormContainer').classList.add('active');
        }
        
        // 删除会议室
        async function deleteRoom(roomId) {
            if (confirm('确定要删除这个会议室吗？相关预约也将被删除。')) {
                rooms = rooms.filter(r => r.id !== roomId);
                reservations = reservations.filter(res => res.room !== roomId);
                
                const roomSuccess = await saveRooms();
                const resSuccess = await saveReservations();
                
                if (roomSuccess && resSuccess) {
                    displayRooms();
                    showSuccess('会议室删除成功！');
                } else {
                    showError('删除失败，请重试');
                }
            }
        }
        
        // 会议室表单提交
        document.getElementById('roomForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const name = document.getElementById('roomName').value;
            const capacity = parseInt(document.getElementById('roomCapacity').value);
            const description = document.getElementById('roomDescription').value;
            const equipment = document.getElementById('roomEquipment').value;
            const status = document.getElementById('roomStatus').value;
            
            if (!name || !capacity) {
                showError('请填写会议室名称和容量');
                return;
            }
            
            let isEdit = false;
            if (currentEditingRoom) {
                // 编辑现有会议室
                const roomIndex = rooms.findIndex(r => r.id === currentEditingRoom);
                if (roomIndex !== -1) {
                    rooms[roomIndex] = {
                        id: currentEditingRoom,
                        name: name,
                        capacity: capacity,
                        description: description,
                        equipment: equipment,
                        status: status
                    };
                    isEdit = true;
                }
            } else {
                // 添加新会议室
                const newRoom = {
                    id: 'room' + Date.now(),
                    name: name,
                    capacity: capacity,
                    description: description,
                    equipment: equipment,
                    status: status
                };
                rooms.push(newRoom);
            }
            
            const success = await saveRooms();
            if (success) {
                // 显示成功提示
                const message = isEdit ? '会议室更新成功！' : '会议室添加成功！';
                showSuccess(message);
                
                document.getElementById('roomFormContainer').classList.remove('active');
                displayRooms();
                
                // 重新加载数据确保同步
                await loadRooms();
                displayRooms();
            } else {
                showError('保存失败，请重试');
            }
        });
        
        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
        
        // 显示成功消息
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>